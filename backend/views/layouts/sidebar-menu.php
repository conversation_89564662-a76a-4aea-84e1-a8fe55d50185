<?php
use common\widgets\Menu;

echo Menu::widget(
    [
        'options' => [
            'class' => 'sidebar-menu',
            'visible' => (Yii::$app->user->identity->role != "user")

        ],
        'items' => [
            [
                'label' => Yii::t('app', 'Dashboard'),
                'url' => Yii::$app->homeUrl,
                'icon' => 'fa-dashboard',
                'active' => Yii::$app->request->url === Yii::$app->homeUrl
            ],

            [
                'label' => Yii::t('app', 'Settings'),
                'url' => ['#'],
                'icon' => 'fa fa-spinner',
                'options' => [
                    'class' => 'treeview',
                ],
                'visible' => Yii::$app->user->can('readPost'),
                'items' => [
                    [
                        'label' => Yii::t('app', 'Basic'),
                        'url' => ['/basic/index'],
                        'icon' => 'fa fa-user',
                    ],
                    [
                        'label' => Yii::t('app', 'Advanced'),
                        'url' => ['/advanced/index'],
                        'icon' => 'fa fa-lock',
                    ],
                ],
            ],
            // if (\Yii::$app->authManager->getRolesByUser($user_id) == "admin"){
            [
                'label' => Yii::t('app', 'User'),
                'url' => ['/user/index'],//Yii::$app->homeUrl,
                'icon' => 'fa fa-user',
                'visible' => (Yii::$app->user->identity->role != "user")
                // 'active' => Yii::$app->request->url === Yii::$app->homeUrl
            ],
            [
                'label' => Yii::t('app', 'Role'),
                'url' => ['/role/index'],//Yii::$app->homeUrl,
                'icon' => 'fa fa-lock',
                'visible' => (Yii::$app->user->identity->role != "user")
                // 'active' => Yii::$app->request->url === Yii::$app->homeUrl
            ],

            [
                'label' => Yii::t('app', 'Salesman Profiles'),
                'url' => ['/salesman-profile/index'],
                'icon' => 'fa fa-user',
                'visible' => (Yii::$app->user->identity->role != "user")
                //'visible' => (Yii::$app->user->identity->username == 'admin'),
            ],
            [
                'label' => Yii::t('app', 'Customer Profiles'),
                'url' => ['/customer-profile/index'],
                'icon' => 'fa fa-user',
                // 'visible' => (Yii::$app->user->identity->role != "user")
            ],
            [
                'label' => Yii::t('app', 'Generate Barcodes'),
                'url' => ['/order/barcode'],
                'icon' => 'fa fa-barcode',
                // 'visible' => (Yii::$app->user->identity->role != "user")
            ],
            //end if condition
            // }                

            [
                'label' => Yii::t('app', 'Add Inventory'),
                'url' => ['/shade/index'],
                'icon' => 'fa fa-user',
                // 'visible' => (Yii::$app->user->identity->role != "user")
            ],

            [
                'label' => Yii::t('app', 'Shades'),
                'url' => ['/shade/increase-quantity'],
                'icon' => 'fa fa-adjust',
                'visible' => (Yii::$app->user->identity->role != "user")
            ],

            [
                'label' => Yii::t('app', 'Purchase'),
                'url' => ['/purchase/index'],
                'icon' => 'fa fa-shopping-cart',
                'visible' => (Yii::$app->user->identity->role != "user")
            ],

            [
                'label' => Yii::t('app', 'Ledger'),
                'url' => ['/ledger/index'],
                'icon' => 'fa fa-file',
                'visible' => (Yii::$app->user->identity->role != "user")
            ],

            [
                'label' => Yii::t('app', 'Ledger 1 A&H Traders'),
                'url' => ['/ledger1/index'],
                'icon' => 'fa fa-file',
                'visible' => (Yii::$app->user->identity->role != "user")
            ],

            [
                'label' => Yii::t('app', 'Ledger 2 AA Enterprise'),
                'url' => ['/ledger2/index'],
                'icon' => 'fa fa-file',
                'visible' => (Yii::$app->user->identity->role != "user")
            ],

            [
                'label' => Yii::t('app', 'Orders'),
                'url' => ['/order/index'],
                'icon' => 'fa fa-edit',
                // 'visible' => (Yii::$app->user->identity->role != "user")
            ],

            [
                'label' => Yii::t('app', 'ASL Traders'),
                'url' => ['#'],
                'icon' => 'fa fa-user',
                'visible' => (Yii::$app->user->identity->role != "user"),
                'options' => [
                    'class' => 'treeview',
                ],
                'items' => [
                    [
                        'label' => Yii::t('app', 'Ledger'),
                        'url' => ['/ledger3/index'],
                        'icon' => 'fa fa-file',
                    ],
                    [
                        'label' => Yii::t('app', 'Purchase'),
                        'url' => ['/purchase3/index'],
                        'icon' => 'fa fa-shopping-cart',
                    ],
                ],
            ],

            [
                'label' => Yii::t('app', 'Elastic'),
                'url' => ['#'],
                'icon' => 'fa fa-user',
                'visible' => (Yii::$app->user->identity->role != "user"),
                'options' => [
                    'class' => 'treeview',
                ],
                'items' => [
                    [
                        'label' => Yii::t('app', 'Ledger'),
                        'url' => ['/ledger4/index'],
                        'icon' => 'fa fa-file',
                    ],
                    [
                        'label' => Yii::t('app', 'Purchase'),
                        'url' => ['/purchase4/index'],
                        'icon' => 'fa fa-shopping-cart',
                    ],
                ],
            ],


        ]
    ]
);