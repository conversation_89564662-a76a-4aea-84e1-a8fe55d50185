<?php

use yii\helpers\Html;
use common\models\Shade;
use yii\widgets\DetailView;
use yii\helpers\ArrayHelper;


/* @var $this yii\web\View */
/* @var $model common\models\Order */

$this->title = $model->order_id;
$this->params['breadcrumbs'][] = ['label' => 'Orders', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="order-view">

    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('Update', ['update', 'id' => $model->order_id], ['class' => 'btn btn-primary']) ?>
        <?= Html::a('Delete', ['delete', 'id' => $model->order_id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]) ?>
    </p>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'order_id',
            'customer_id',
            'salesman_id',
            'order_date',
            'status',
            'created_at',
            'approved_at',
            'delivered_at',
            'cancelled_at',
        ],
    ]) ?>










<?php 


        $shades = Shade::find()->orderBy('shade_name')->asArray()->all(); 
         // create an array of pairs ('id', 'type-name'):
        $shadeList = ArrayHelper::map($shades, 'shade_id', 'shade_name'); 
         // finally create the drop-down list:
?>

<?php
// $d='2';
    // print_r($shadeList[$d]);
    // die;

//    foreach ($shadeList as $key) {

        //print_r($key);
    // die;

        # code...
    
//
//}
$len=sizeof($shadeList);
$ptr=1;
$ptr2=1;
$shadeptr = 0;
$indptr=0;
$var=18;
$type="create";
$ind2=0;
// $len2=sizeof($model2);
// print_r($shadeList[3])
//echo ( $form->field($model2, 'shade_id')->checkBoxList($shadeList));
?>



<style>
 table { table-layout: fixed; }
 /*table tbody tr th{  background-color: #b0e0e6;} */
 table th, table td { overflow: hidden;text-align: center; }
</style>
 <div class="box">
<div class="box-body">
              <table id="example2" class="table table-bordered table-hover">
                <thead>

                </thead>


                <tbody>


<?php                
                while ($ptr<=$len)
                {
?>                    

                <tr>

<?php

                    // $var=1;
                    for ($i = 0 ;$i<$var;$i++){
         ?>
                            <th style="width: 5.5%;background-color: #189FEC;font-size:small;">
                                <?php
                                    echo $shadeList[$ptr];
                                    $ptr=$ptr+1; 
                                    if ($ptr>$len){
                                        break;
                                    }
                                ?>
                            </th>
<?php
                }

?>
    
                </tr>
                
                

                <tr>

<?php

// echo '<pre>';
// print_r($model);
// die;
                    // $var=18;
?>



<?php

// echo '<pre>';
// print_r($model);
// die;
                    // $var=18;
if ($type=='create')
{
                    for ($i = 0 ;$i<$var;$i++){
         ?>
                            <td >
                                <?php
                                     
// print_r($model2[0]);

// print_r($model);
// die;
// print_r($ptr2);
// die;
                                     //echo $form->field($model2[$ptr2-1],'quantity')->textinput(['maxlength' => true])->label(false);
//                                     echo $form->field($model,'quantity[]')->checkbox(array('label'=>''));//textinput(['maxlength' => true,])->label(false);
  

//my code here (uncommment above line)
                                
// echo "<pre>";
// print_r($model2[$shadeptr]->shade_id);
// die;


if ($shadeptr< sizeof($model2) && $model2[$shadeptr]->shade_id == $ptr2){
?>
  
  <div style="font-weight:bold;">
    <?php
 echo $model2[$shadeptr]->quantity;
 ?>
  </div> 
  
    <?php



    // echo $model2[$shadeptr]->quantity;
    $shadeptr=$shadeptr+1;
}


else
    echo " ";

?>

 <!-- <input type="checkbox" name="quantity1[]" value = <?php echo $ptr2; ?> ></input> -->


<?php

                                     // die;
                                     $ptr2=$ptr2+1;
                                     if ($ptr2>$len){
                                        break;
                                     }

                                ?>

                            </td>


<?php
                } //end for
            }// end if

            ?>

                </tr>

<?php
            }
?>            

                </tbody>


                
                <tfoot>
                <tr>

<?php

                    // $var=18;
                    for ($i = 0 ;$i<$var;$i++){
         ?>
         <!-- 
                            <th><?php
                            // echo 0 
                            ?></th>
                             -->
<?php
                }

?>
                </tr>


                </tfoot>
              </table>
            </div>
            <!-- /.box-body -->
          </div>
          <!-- /.box -->
</div>
