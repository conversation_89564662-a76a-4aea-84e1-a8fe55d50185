<?php

use yii\helpers\Html;
use common\models\Shade;
use yii\widgets\DetailView;
use yii\helpers\ArrayHelper;
use common\models\CustomerProfile;
use common\models\SalesmanProfile;
use common\models\Order;
use yii\bootstrap\Alert;
/* @var $this yii\web\View */
/* @var $model common\models\Order */






if ($type=="viewpdf")
{

$this->title = "Color Box";//$model->order_id;
$this->params['breadcrumbs'][] = ['label' => 'Orders', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>




<div class="order-view">

    <div style="text-align:center;">
        <h2>Syed Enterprises<br>
            <span style="font-size:25px;">(<?= Html::encode($this->title) ?>)
        <span style="font-size:10px;margin-top:0px;"><br>Address: North Karachi Industrial Area, Karachi  <br> Cell: 0324-2949207, 0335-2573075  Email: <EMAIL></span>
    </span>
</h2>
    </div>



<div>


    <div style="float: left; width: 54%;">
<b>Customer Name: </b><?php echo CustomerProfile::findOne($model->customer_id)->customer_name ?>


    </div>

    <div style="float: right; width: 28%;">
<b>Date: </b><?php echo date("d-m-Y"); ?>
    
    </div>
</div>


<div>

    <div style="float: left; width: 54%;">
<b>Address: </b><?php echo CustomerProfile::findOne($model->customer_id)->address ?>
    

    </div>
        <div style="float: right; width: 28%;">

    <b>Phone: </b><?php echo SalesmanProfile::findOne($model->salesman_id)->phone_number ?>
    <!-- This is text that is set to float:right. -->


    </div>

</div>

<div>
    <br>
    <b>Salesman: </b><?php echo SalesmanProfile::findOne($model->salesman_id)->salesman_name ?>
</div>



<div>
    <b>Total Boxes: </b><?php

    $iter=0;
    $sum=0;
    foreach ($model2 as $key => $value) {
        $sum+=$model2[$iter]->quantity;
        $iter+=1;

        # code...
    }



$str="Total Boxes: ".$sum;

     echo $str;
     ?>
</div>




<br>
<br>
<?php
}

/*
//////////////////////////////////////////
//////////////////////////////////////////



*/
elseif ($type=="view") {

$this->title = $model->order_id;
$this->params['breadcrumbs'][] = ['label' => 'Orders', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

  \Yii::$app->getSession()->getFlash('success');


?>

<div class="order-view">

    <h1><?php
    // echo Html::encode($this->title) ?></h1>

    <p>

<?php
if (Order::findOne($model->order_id)->status=="Approved" )//&& Yii::$app->user->identity->name=="Admin")
{

?>
<div style="float:right;">
        <?= Html::a('Get Packing List', ['viewpdf', 'id' => $model->order_id], ['class' => 'btn btn-success']) ?>

</div>
<?php
}

else
{

echo "Note: Packing List will be printed after the order is Approved!";
?>
<br>
<?php
}


 if (Yii::$app->user->identity->name=="Admin"){
?>
            

        <?= Html::a('Update', ['update', 'id' => $model->order_id], ['class' => 'btn btn-primary']) ?>
 <?php } ?>       
        <?php
        // echo Html::a('Delete', ['delete', 'id' => $model->order_id], [
        //     'class' => 'btn btn-danger',
        //     'data' => [
        //         'confirm' => 'Are you sure you want to delete this item?',
        //         'method' => 'post',
        //     ],
        // ]) 
        ?>

    </p>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            // 'order_id',

            // 'customerprofile.customer_name',
            [   
                'attribute'=>'customer_id',
                'value'=>   CustomerProfile::findOne($model->customer_id)->customer_name,
            ],


            [   
                'attribute'=>'salesman_id',
                'value'=>   SalesmanProfile::findOne($model->salesman_id)->salesman_name,
            ],

            // 'salesman_id',
            'order_date',
            'status',
            // 'created_at',
            'approved_at',
            'delivered_at',
            // 'cancelled_at',
        ],
    ]) 


?>
<?php
/*
//////////////////////////////
/////////////////////////////
*/
}

?>


<?php 


        $shades = Shade::find()->orderBy('shade_name')->asArray()->all(); 
         // create an array of pairs ('id', 'type-name'):
        $shadeList = ArrayHelper::map($shades, 'shade_id', 'shade_name'); 
         // finally create the drop-down list:
?>

<?php
// $d='2';
    // print_r($shadeList[$d]);
    // die;

//    foreach ($shadeList as $key) {

        //print_r($key);
    // die;

        # code...
    
//
//}
$len=sizeof($shadeList);
$ptr=1;
$ptr2=1;
$shadeptr = 0;
$indptr=0;
$var=25;
$type="create";
$ind2=0;
// $len2=sizeof($model2);
// print_r($shadeList[3])
//echo ( $form->field($model2, 'shade_id')->checkBoxList($shadeList));
?>



<style>
 /*table { table-layout: fixed; }*/
  table { table-layout: fixed; border-collapse: collapse;}

 /*table tbody tr th{  background-color: #b0e0e6;} */
 table th, table td { overflow: hidden;text-align: center; }
table, th, td {
    border: 0.1px solid black;
}

</style>
 <div class="box">
<div class="box-body">
              <table id="example2" class="table table-bordered table-hover">
                <thead>

                </thead>


                <tbody>


<?php                
                while ($ptr<=$len)
                {
?>                    

                <tr>

<?php

                    // $var=1;
                    for ($i = 0 ;$i<$var;$i++){
         ?>
                            <th style="width: 5.5%;background-color: #D0D0D0;font-size:10px;">
                                <?php
                                    echo $shadeList[$ptr];
                                    $ptr=$ptr+1; 
                                    if ($ptr>$len){
                                        break;
                                    }
                                ?>
                            </th>
<?php
                }

?>
    
                </tr>
                
                

                <tr>

<?php

// echo '<pre>';
// print_r($model);
// die;
                    // $var=18;
?>



<?php

// echo '<pre>';
// print_r($model);
// die;
                    // $var=18;
if ($type=='create')
{
                    for ($i = 0 ;$i<$var;$i++){
         ?>
                            <td style="height:24px;">
                                <?php
                                     
// print_r($model2[0]);

// print_r($model);
// die;
// print_r($ptr2);
// die;
                                     //echo $form->field($model2[$ptr2-1],'quantity')->textinput(['maxlength' => true])->label(false);
//                                     echo $form->field($model,'quantity[]')->checkbox(array('label'=>''));//textinput(['maxlength' => true,])->label(false);
  

//my code here (uncommment above line)
                                
// echo "<pre>";
// print_r($model2[$shadeptr]->shade_id);
// die;


if ($shadeptr< sizeof($model2) && $model2[$shadeptr]->shade_id == $ptr2){
?>
  
  
    
<div  style="font-weight:bold;">
    <?php

 echo $model2[$shadeptr]->quantity;
 ?>
</div>
    <?php



    // echo $model2[$shadeptr]->quantity;
    $shadeptr=$shadeptr+1;
}


else
    echo " ";

?>

 <!-- <input type="checkbox" name="quantity1[]" value = <?php echo $ptr2; ?> ></input> -->


<?php

                                     // die;
                                     $ptr2=$ptr2+1;
                                     if ($ptr2>$len){
                                        break;
                                     }

                                ?>

                            </td>


<?php
                } //end for
            }// end if

            ?>

                </tr>

<?php
            }
?>            

                </tbody>


                
                <tfoot>
                <tr>

<?php

                    // $var=18;
                    for ($i = 0 ;$i<$var;$i++){
         ?>
         <!-- 
                            <th><?php
                            // echo 0 
                            ?></th>
                             -->
<?php
                }

?>
                </tr>


                </tfoot>
              </table>
            </div>
            <!-- /.box-body -->
          </div>
          <!-- /.box -->
</div>
