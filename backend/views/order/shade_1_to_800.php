<?php

use yii\helpers\Html;
use common\models\Shade;
use yii\widgets\DetailView;
use yii\helpers\ArrayHelper;
use common\models\CustomerProfile;
use common\models\SalesmanProfile;
use common\models\Order;
use common\models\OrderDetail;

// use yii\helpers\ArrayHelper;

use yii\bootstrap\Alert;
?>
<?php


$shades = Shade::find()->orderBy('shade_name')->asArray()->all();
// create an array of pairs ('id', 'type-name'):
$shadeList = ArrayHelper::map($shades, 'shade_id', 'shade_name');
// finally create the drop-down list:
?>

<?php

$white = Shade::find()->where(['=', 'shade_name', 'White'])->one();
$len = $white->shade_id; //sizeof($shadeList)-3;
// print_r($len);
$ptr = 1;
$ptr2 = 1;
$shadeptr = 0;
$indptr = 0;
$var = 20;
// $type="create";
$ind2 = 0;
$shade_with_name_ind = 801;

?>

<style>
    /*table { table-layout: fixed; }*/

    table {
        table-layout: fixed;
        border-collapse: collapse;
    }

    /*table tbody tr th{  background-color: #b0e0e6;} */
    table th,
    table td {
        overflow: hidden;
        text-align: center;
    }

    table,
    th,
    td {
        border: 0.1px solid black;
    }
</style>
<div class="box">
    <div class="box-body">
        <?php

        // $shade_with_names = Shade::find()->select('shade_name')->where(['>', 'shade_id', 800])->column();
        // print_r($shade_quantites);

        // }
        $type = 'create';
        ?>


        <table class="table table-bordered">

            <tbody>



                <?php
                $ptr_with_names = $len;
                $ptr_with_names_values = $len-1;
                for ($i = 0; $i < 4; $i++) {
                ?>
                    <tr>
                        <?php
                        for ($j = 0; $j < 5; $j++) {
                        ?>
                            <th style="width:40%;background-color: #D0D0D0;">
                                <?php
                                if ($ptr_with_names <= $len + 19) {
                                    echo $shadeList[$ptr_with_names];
                                    $ptr_with_names = $ptr_with_names + 1;
                                } else {

                                    echo " ";
                                }
                                ?>
                            </th>
                        <?php
                        }
                        ?>
                    </tr>

                    <tr>
                    <?php
                        for ($j = 0; $j < 5; $j++) {
                        ?>
                            <td style="height:30px;">
                                <?php
                                if ($ptr_with_names_values <= $len + 19) {
                                    if (isset($model2[$ptr_with_names_values])) {
                                        echo $model2[$ptr_with_names_values]->quantity;
                                    } else {
                                        echo " ";
                                    }

                                    $ptr_with_names_values = $ptr_with_names_values + 1;
                                } else {

                                    echo " ";
                                }
                                ?>
                            </td>
                        <?php
                        }
                        ?>
                    </tr>
                <?php
                }
                ?>





            </tbody>


        </table>


        <table id="example2" class="table table-bordered table-hover">
            <thead>

            </thead>


            <tbody>

                <?php
                /// Without names
                while ($ptr < $len) {
                ?>

                    <tr>

                        <?php

                        // $var=1;
                        for ($i = 0; $i < $var; $i++) {
                        ?>
                            <th style="width:6%;background-color: #D0D0D0;font-size:10px;">
                                <?php
                                echo $shadeList[$ptr];
                                $ptr = $ptr + 1;
                                if ($ptr >= $len) {
                                    break;
                                }
                                ?>
                            </th>
                        <?php
                        }

                        ?>

                    </tr>



                    <tr>

                        <?php

                        // echo '<pre>';
                        // print_r($model);
                        // die;
                        // $var=18;
                        ?>



                        <?php

                        // echo '<pre>';
                        // print_r($model);
                        // die;
                        // $var=18;
                        if (1) //$type=='create')
                        {
                            for ($i = 0; $i < $var; $i++) {
                        ?>
                                <td style="height:30px;">
                                    <?php

                                    // print_r($model2[0]);

                                    // print_r($model);
                                    // die;
                                    // print_r($ptr2);
                                    // die;
                                    //echo $form->field($model2[$ptr2-1],'quantity')->textinput(['maxlength' => true])->label(false);
                                    //                                     echo $form->field($model,'quantity[]')->checkbox(array('label'=>''));//textinput(['maxlength' => true,])->label(false);


                                    //my code here (uncommment above line)

                                    // echo "<pre>";
                                    // print_r($model2[$shadeptr]->shade_id);
                                    // die;


                                    if ($shadeptr < sizeof($model2) && $model2[$shadeptr]->shade_id == $ptr2) {
                                    ?>



                                        <div style="font-weight:bold;">
                                            <?php

                                            echo $model2[$shadeptr]->quantity;
                                            ?>
                                        </div>
                                    <?php



                                        // echo $model2[$shadeptr]->quantity;
                                        $shadeptr = $shadeptr + 1;
                                    } else
                                        echo " ";

                                    ?>

                                    <!-- <input type="checkbox" name="quantity1[]" value = <?php echo $ptr2; ?> ></input> -->


                                    <?php

                                    // die;
                                    $ptr2 = $ptr2 + 1;
                                    if ($ptr2 >= $len) {
                                        break;
                                    }

                                    ?>

                                </td>


                        <?php
                            } //end for
                        } // end if

                        ?>

                    </tr>

                <?php
                }
                ?>

            </tbody>



            <tfoot>
                <tr>

                    <?php

                    // $var=18;
                    for ($i = 0; $i < $var; $i++) {
                    ?>
                        <!-- 
                            <th><?php
                                // echo 0 
                                ?></th>
                             -->
                    <?php
                    }

                    ?>
                </tr>


            </tfoot>
        </table>
    </div>
    <!-- /.box-body -->
</div>
<!-- /.box -->