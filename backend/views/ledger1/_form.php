<?php

use common\models\CustomerProfile;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\models\SalesmanProfile;


use yii\helpers\ArrayHelper;

use kartik\date\DatePicker;
use kartik\datetime\DateTimePicker;


  \Yii::$app->getSession()->getFlash('error');

?>

<div class="order-form">

    <?php $form = ActiveForm::begin(); ?>
    
    <div class="col-md-4">

        <!-- Type Debit/Credit -->
          <?php $personTypeList = ['salesman' => 'Salesman', 'customer' => 'Customer']; ?>
          <?= $form->field($model, 'person_type')->dropDownList($personTypeList) ?>
        <!-- Type Debit/Credit -->

        <!-- Salesman -->
        <?php 
          $salesman = SalesmanProfile::find()->orderBy(['salesman_name'=>SORT_ASC])->asArray()->all(); 
          $salesmanList = ArrayHelper::map($salesman, 'salesman_id', 'salesman_name'); 
        ?>
        <?= $form->field($model, 'salesman_table_id')->dropDownList($salesmanList)->label('Salesman &nbsp&nbsp&nbsp&nbsp'.Html::a('(Create Salesman)', ['salesman-profile/create'])) ?>
        <!-- Salesman -->

        <!-- Customer -->
        <?php 
          $Customer = CustomerProfile::find()->orderBy(['customer_name'=>SORT_ASC])->asArray()->all(); 
          $CustomerList = ArrayHelper::map($Customer, 'customer_id', 'customer_name'); 
        ?>
        <?= $form->field($model, 'customer_table_id')->dropDownList($CustomerList)->label('Customer &nbsp&nbsp&nbsp&nbsp'.Html::a('(Create Customer)', ['customer-profile/create'])) ?>
        <!-- Customer -->

        <!-- Type Debit/Credit -->
        <?= $form->field($model, 'particular') ?>
        <!-- Type Debit/Credit -->

        <!-- Type Debit/Credit -->
        <?php $typeList = ['credit' => 'Credit', 'debit' => 'Debit']; ?>
        <?= $form->field($model, 'type')->dropDownList($typeList) ?>
        <!-- Type Debit/Credit -->

        <!-- Type Debit/Credit -->
        <?= $form->field($model, 'amount') ?>
        <!-- Type Debit/Credit -->

    
        <?= 
          $form->field($model, 'ledger1_date')->widget(DatePicker::classname(), [
              'options' => ['label'=>"Joining Date",'placeholder' => "Select Date",'style'=>'width:420px'],
              'pluginOptions' => [
                'format' => 'yyyy-mm-d',
              ]
          ])
        ?>

    </div><!-- col-md-4 -->


    <div class="col-md-12">
        <?= Html::submitButton($model->isNewRecord ? 'Add Ledger Entry' : 'Update Ledger Entry', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>


    <?php ActiveForm::end(); ?>

</div>

<?php    
  $this->registerJs('

    $(document).ready(function() {
      $(".field-ledger1-customer_table_id").hide();
    });


    $("#ledger1-person_type").change(function() {
      console.log(this.value);
      if (this.value == "customer") {
        $(".field-ledger1-salesman_table_id").hide("slow");
        $(".field-ledger1-customer_table_id").show("slow");
      } else {
        $(".field-ledger1-salesman_table_id").show("slow");
        $(".field-ledger1-customer_table_id").hide("slow");
      }

    });'
  )

?>