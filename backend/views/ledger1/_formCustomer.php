<?php

use common\models\CustomerProfile;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\models\SalesmanProfile;


use yii\helpers\ArrayHelper;

use kartik\date\DatePicker;
use kartik\datetime\DateTimePicker;


  \Yii::$app->getSession()->getFlash('error');

?>

<div class="order-form">

    <?php $form = ActiveForm::begin(); ?>
    
    <div class="col-md-4">

        <!-- Type Debit/Credit -->
        <?= $form->field($model, 'particular') ?>
        <!-- Type Debit/Credit -->

        <!-- Type Debit/Credit -->
        <?php $typeList = ['credit' => 'Credit', 'debit' => 'Debit']; ?>
        <?= $form->field($model, 'type')->dropDownList($typeList) ?>
        <!-- Type Debit/Credit -->

        <!-- Type Debit/Credit -->
        <?= $form->field($model, 'amount') ?>
        <!-- Type Debit/Credit -->

    
        <?= 
          $form->field($model, 'ledger1_date')->widget(DatePicker::classname(), [
              'options' => ['label'=>"Joining Date",'placeholder' => "Select Date",'style'=>'width:420px'],
              'pluginOptions' => [
                'format' => 'yyyy-mm-d',
              ]
          ])
        ?>

    </div><!-- col-md-4 -->


    <div class="col-md-12">
        <?= Html::submitButton($model->isNewRecord ? 'Add Ledger Entry' : 'Update Ledger Entry', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>


    <?php ActiveForm::end(); ?>

</div>