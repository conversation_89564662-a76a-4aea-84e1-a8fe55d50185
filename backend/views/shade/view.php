<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\Shade */

$this->title = $model->shade_id;
$this->params['breadcrumbs'][] = ['label' => 'Shades', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="shade-view">

    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('Update', ['update', 'id' => $model->shade_id], ['class' => 'btn btn-primary']) ?>
        <?= Html::a('Delete', ['delete', 'id' => $model->shade_id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]) ?>
    </p>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'shade_id',
            'shade_name',
            'gender',
            'quantity',
            'status',
            'created_at',
        ],
    ]) ?>

</div>
