<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\Shade */
/* @var $form yii\widgets\ActiveForm */
  \Yii::$app->getSession()->getFlash('error');

?>

<div class="inventory-form">

    <?php $form = ActiveForm::begin(); ?>
    <div class="row col-md-12" >
    
        <div class="col-md-6">
        <?= $form->field($model, 'shade_id')->textarea(['maxlength' => true,'rows' => 6,'style'=>'width:500px','id'=>'inputfield','oninput'=>"myfunction()"]) ?>
      </div>
      <div class=" col-md-4">
        <b>Tracking:</b>
        <div id="myid" class="nav-tabs-custom bg-blue"></div>
      </div>  
    </div>

     <?php
     //echo $form->field($model, 'created_at')->textInput() 
    ?>

    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Add Into Inventory' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>

    </div>

    <?php ActiveForm::end(); ?>

</div>



<script>
var data = [];//new Array();
      // data['0'] = 'hi';
      // data['1'] = 'bye';
      // data['4']='sdf';
      // data['3']='3';

      var newHTML = [];
/////

/////


  function myfunction(){
        var data=[];
// console.log($id);
    // newHTML.push('<span>' + data[0] + '</span>');
  //   for (var key in data) {
  // // console.log("key " + key + " has value " + myArray[key]);
  //   newHTML.push('<span>' key +" "+ data[key] + '</span>');

  //     }
  // var lastWord = document.getElementById("inputfield").value.match(/\w+$/)[0];
// var lastWord = document.getElementById("inputfield").value.split("-").pop();

// var lastWord = document.getElementById("inputfield").value.split(" ");
// lastWord= lastWord[lastWord.length - 1];
var lastWord = document.getElementById("inputfield").value.split("\n");//.split("/n").splice(-1)[0];

console.log(document.getElementById("inputfield").value);
console.log(document.getElementById("inputfield").value.length);

console.log(lastWord);

// console.log(lastWord[lastWord.length - 2]);
lastWord=lastWord[lastWord.length - 2];
// 
var allWords=document.getElementById("inputfield").value.split("\n");
//loop begins below

allWords.forEach(function(lastWord)
{
  if (lastWord=="\n")
  {  return true;}
  if(lastWord[0]=='0')
  {
    lastWord=lastWord.substr(1);
  }
  if(lastWord[0]=='0')
  {
   lastWord=lastWord.substr(1); 
  }


  if (lastWord=='801')
  {
    lastWord='White';
  }
  else if(lastWord=='802')
  {
    lastWord='Black'; 
  }
  else if(lastWord=='803')
  {
    lastWord='Red';
  }
  // 


          // else 
          
          if (lastWord == "")
          {console.log("hhhhhhh");}
          // else
           else
            if (lastWord in data){
              data[lastWord]+=1;

          }
          else{
            data[lastWord]=1;
          }
        // for (var i = 0; i < data.length; i++) {



});//loop ends        
        var newHTML = [];

        for (var i in data){
          // console.log( data[i]);
    newHTML.push('<div>' + '<span>' + "Shade " + i+ '</span>' + '<span style="float:right">'+"   Quantity: "+data[i] +'</span>'+ '</div>' );
}
// newHTML="underconstruction";


    document.getElementById("myid").innerHTML=newHTML.join(" ");
 }
/*
// console.log($id);
    // newHTML.push('<span>' + data[0] + '</span>');
  //   for (var key in data) {
  // // console.log("key " + key + " has value " + myArray[key]);
  //   newHTML.push('<span>' key +" "+ data[key] + '</span>');

  //     }
  // var lastWord = document.getElementById("inputfield").value.match(/\w+$/)[0];
// var lastWord = document.getElementById("inputfield").value.split("-").pop();

// var lastWord = document.getElementById("inputfield").value.split(" ");
// lastWord= lastWord[lastWord.length - 1];
var lastWord = document.getElementById("inputfield").value.split("\n");//.split("/n").splice(-1)[0];

// console.log(lastWord);
// console.log(lastWord[lastWord.length - 2]);
lastWord=lastWord[lastWord.length - 2]


if(lastWord[0]=='0')
{
  lastWord=lastWord.substr(1);
}
if(lastWord[0]=='0')
{
 lastWord=lastWord.substr(1); 
}


if (lastWord=='801')
{
  lastWord='White';
}
else if(lastWord=='802')
{
  lastWord='Black'; 
}
else if(lastWord=='803')
{
  lastWord='Red';
}
        var newHTML = [];



        if (lastWord in data){
            data[lastWord]+=1;

        }
        else{
          data[lastWord]=1;
        }
      // for (var i = 0; i < data.length; i++) {
        for (var i in data){
          // console.log( data[i]);
    newHTML.push('<div>' + '<span>' + "Shade " + i+ '</span>' + '<span style="float:right">'+"   Quantity: "+data[i] +'</span>'+ '</div>' );
}
// newHTML="underconstruction";


    document.getElementById("myid").innerHTML=newHTML.join(" ");
 
  }
*/
        window.onload = myfunction;

</script>