<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\models\SalesmanProfile;


use yii\helpers\ArrayHelper;

use kartik\date\DatePicker;
use kartik\datetime\DateTimePicker;


  \Yii::$app->getSession()->getFlash('error');

?>

<div class="order-form">

    <?php $form = ActiveForm::begin(); ?>
    
    <div class="col-md-12">
      <div class="col-md-6">

        <!-- Type Debit/Credit -->
        <?= $form->field($model, 'shade_id')->label('Quantity') ?>
        <!-- Type Debit/Credit -->

      </div> <!-- col-md-8 -->
    </div><!-- col-md-12 -->


    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Add' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>