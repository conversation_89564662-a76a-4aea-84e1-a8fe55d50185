<?php

use yii\helpers\Html;
use yii\grid\GridView;

use common\models\SalesmanProfile;
use yii\helpers\Url;

$this->title = 'Ledgers';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="order-index">

 <h1><?= Html::encode($this->title) ?></h1> 

    <p>
        <?php
            if (Yii::$app->user->identity->name=="Admin") {
                echo Html::a('Create Ledger', ['create'], ['class' => 'btn btn-success']);
            }
        ?>
    </p>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],
            [
                'attribute'=> 'salesman',
                'value' => function($model) {
                    return ($model->salesman ? $model->salesman->salesman_name : '-');
                }
            ],
            [
                'attribute'=> 'customer',
                'value' => function($model) {
                    return ($model->customer ? $model->customer->customer_name : '-');
                }
            ],
            [
                'label' => 'Current Balance',
                'format' => 'html',
                'value' => function($model) {
                    $val = $model->balance(($model->salesman_table_id ? $model->salesman_table_id : $model->customer_table_id) , ($model->salesman_table_id ? 1 : 2) );
                    return '<span style="'.($val < 0 ? "color:red;" : "color:green;" ).'"><b>'.$val.'</b></span>';
                }
            ],
            [
                'attribute' => 'ledger_date',
                'label' => 'Last Ledger At',
                'format' =>  ['date', 'php:d-m-Y'],
            ],

            [
                'class' => 'yii\grid\ActionColumn',
                'header' => 'Action',
                'headerOptions' => ['style' => 'color:#337ab7'],
                'template' => '{view} {delete}',
                'urlCreator' => function( $action, $model, $key, $index ){
                    if ($action == "view") {
                        if ($model->salesman_table_id) {
                            return Url::to(['salesman', 'id' => $model->salesman_table_id]);
                        } else {
                            return Url::to(['customer', 'id' => $model->customer_table_id]);
                        }
                    } elseif ($action == "delete") {
                        if ($model->salesman_table_id) {
                            return Url::to(['delete-all', 'id' => $model->salesman_table_id, 'type' => 1]);
                        } else {
                            return Url::to(['delete-all', 'id' => $model->customer_table_id, 'type' => 2]);
                        }
                    }
                },
                'buttons' => [
                    'view' => function ($url, $model) {
                        return Html::a('<span class="glyphicon glyphicon-eye-open"></span>', $url, [
                            'title' => Yii::t('app', 'View Entries'),
                        ]);
                    },
                    'delete' => function ($url, $model) {
                        if (Yii::$app->user->identity->name == "Admin") {
                            $entityName = $model->salesman_table_id ?
                                ($model->salesman ? $model->salesman->salesman_name : 'Salesman') :
                                ($model->customer ? $model->customer->customer_name : 'Customer');

                            return Html::a('<span class="glyphicon glyphicon-trash"></span>', $url, [
                                'title' => Yii::t('app', 'Delete All Entries'),
                                'data-confirm' => "Are you sure you want to delete ALL ledger entries for {$entityName}? This action cannot be undone.",
                                'data-method' => 'post',
                                'style' => 'color: #d9534f; margin-left: 5px;'
                            ]);
                        }
                        return '';
                    },
                ],
            ],
        ],
    ]); ?>
</div>
