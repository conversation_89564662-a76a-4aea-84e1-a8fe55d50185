<?php

use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\Url;

$this->title = 'Purchases';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="order-index">

 <h1><?= Html::encode($title) ?><span style="font-size:20px"> (Customer)</span></h1> 

    <?php $val = 0; ?>

    <div class="col-md-6" style="padding-left:0px">
        <p>
            <?php
                if (Yii::$app->user->identity->name=="Admin") {
                    echo Html::a('Create Customer Purchase', Url::to(['purchase3/customer-create/', 'id' => $id]) , ['class' => 'btn btn-success']);
                }
            ?>
        </p>
    </div>

    <div class="col-md-6 ">
        <p>
            <?php
                if (Yii::$app->user->identity->name=="Admin") {
                    echo Html::a('Export All', Url::to(['purchase3/customer-pdf-export/', 'id' => $id]) , ['class' => 'btn btn-primary', 'target' => "_blank", "style" => "float: right;"]);
                } 
            ?>
        </p>
    </div>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            [
                'attribute' => 'purchase3_date',
                'label' => 'Purchase Date',
                'format' =>  ['date', 'php:d-m-Y'],
            ],
            [
                'attribute'=> 'particular',
            ],
            [
                'attribute'=> 'debit',
            ],
            [
                'attribute'=> 'credit',
            ],
            [
                'label'=>'Balance',
                'format' => 'html',
                'filter' => array('0' => Yii::t('app', 'No'), '1' => Yii::t('app', 'Yes')),
                'value' => function($model, $key, $index, $column) {
                    global $val;
                    $val += $model->debit - $model->credit;;
                    return '<span style="'.($val < 0 ? "color:red;" : "color:green;" ).'"><b>'.$val.'</b></span>';
                },
            ],
            [
                'class' => 'yii\grid\ActionColumn',
                'visible' => (Yii::$app->user->identity->name=="Admin"),
                'header' => 'Edit',
                'headerOptions' => ['style' => 'color:#337ab7'],
                'template' => '{update}',
                'buttons' => [
                    'update' => function ($url, $model) {
                        return Html::a('<span class="glyphicon glyphicon-pencil"></span>', $url, [
                                    'title' => Yii::t('app', 'Salesman Data'),
                        ]);
                    },
                ],
            ],
        ],
    ]); ?>
</div>
