<?php

use yii\helpers\Html;
use yii\grid\GridView;

use common\models\SalesmanProfile;
use yii\helpers\Url;

$this->title = 'Ledgers (AA Enterprise)';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="order-index">

 <h1><?= Html::encode($this->title) ?></h1> 

    <p>
        <?php
            if (Yii::$app->user->identity->name=="Admin") {
                echo Html::a('Create Ledger', ['create'], ['class' => 'btn btn-success']);
            }
        ?>
    </p>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],
            [
                'attribute'=> 'salesman',
                'value' => function($model) {
                    return ($model->salesman ? $model->salesman->salesman_name : '-');
                }
            ],
            [
                'attribute'=> 'customer',
                'value' => function($model) {
                    return ($model->customer ? $model->customer->customer_name : '-');
                }
            ],
            [
                'label' => 'Current Balance',
                'format' => 'html',
                'value' => function($model) {
                    $val = $model->balance(($model->salesman_table_id ? $model->salesman_table_id : $model->customer_table_id) , ($model->salesman_table_id ? 1 : 2) );
                    return '<span style="'.($val < 0 ? "color:red;" : "color:green;" ).'"><b>'.$val.'</b></span>';
                }
            ],
            [
                'attribute' => 'ledger2_date',
                'label' => 'Last Ledger At',
                'format' =>  ['date', 'php:d-m-Y'],
            ],

            [
                'class' => 'yii\grid\ActionColumn',
                'header' => 'View',
                'headerOptions' => ['style' => 'color:#337ab7'],
                'template' => '{view}',
                'urlCreator' => function( $action, $model, $key, $index ){
                    if ($action == "view") {
                        if ($model->salesman_table_id) {
                            return Url::to(['salesman', 'id' => $model->salesman_table_id]);
                        } else {
                            return Url::to(['customer', 'id' => $model->customer_table_id]);
                        }
                        
                    }
                },
                'buttons' => [
                    'view' => function ($url, $model) {
                        return Html::a('<span class="glyphicon glyphicon-eye-open"></span>', $url, [
                            'title' => Yii::t('app', 'View Entries'),
                        ]);
                    },
                ],
            ],
        ],
    ]); ?>
</div>
