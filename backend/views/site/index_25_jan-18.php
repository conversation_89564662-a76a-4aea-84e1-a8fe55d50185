<?php
/* @var $this yii\web\View */
use dosamigos\chartjs\ChartJs;
$this->title = 'Color box';
?>
    <head>
<script type="text/javascript" src="../views/site/script/canvasjs.min.js"></script>


  <script type="text/javascript">
  window.onload = function () {


    var dataPoints = [];
    var y = 0;
    
    for ( var i = 0; i < 10000; i++ ) {
      
      y += Math.round(5 + Math.random() * (-5 - 5));  
      dataPoints.push({ y: y});
    }
    
    var chart = new CanvasJS.Chart("chartContainer3",
    {
      animationEnabled: true,
      zoomEnabled: true,
      
      title:{
        text: "Performance Demo with 10,000 DataPoints"
      },    
      data: [
      {
        type: "spline",              
        dataPoints: dataPoints
      }
      ]
    });
    chart.render();



// ///////////////////////////////////////
/////////////////////////////////////////
//////////////////////////////////////////


    var chart = new CanvasJS.Chart("chartContainer",
    {
      title:{
        text: "Top 10 Customers of the Month"
      },
      data: [
      {
        type: "bar",
        dataPoints: [
        { y: 185, label: "AA Enterprises"},
        { y: 128, label: "Solstech"},
        { y: 246, label: "ThreadLtd"},
        { y: 272, label: "AAAAA"},
        { y: 200, label: "bb"},
        ]
      },
      //       {
      //   type: "bar",
      //   dataPoints: [
      //   { y: 0, label: "Italy"},
      //   { y: 0, label: "China"},
      //   { y: 0, label: "France"},
      //   { y: 0, label: "Great Britain"},
      //   ]
      // },

      ]
    });

chart.render();

// ///////////////////////////////////////
/////////////////////////////////////////
//////////////////////////////////////////
  var chart = new CanvasJS.Chart("chartContainer2",{
    title: {
      text: "Try Dragging DataPoints to Reposition them"
    },
    axisX:{
      minimum: 5,
      maximum: 95
    },
    data: [
    {
      type: "spline",
      cursor: "move",
      dataPoints: [
        { x: 10, y: 71 },
        { x: 20, y: 55 },
        { x: 30, y: 50 },
        { x: 40, y: 65 },
        { x: 50, y: 95 },
        { x: 60, y: 68 },
        { x: 70, y: 28 },
        { x: 80, y: 34 },
        { x: 90, y: 14 }
      ]
    }         
    ]
  });

  chart.render();
 

  var record = false;
  var precisionLevel = 1;
  var xValue, yValue, parentOffset, relX, relY;
  var selected;
  var timerId = null;
  jQuery(".canvasjs-chart-canvas").last().on({
    mousedown: function(e) {
      parentOffset = $(this).parent().offset();
      relX = e.pageX - parentOffset.left;
      relY = e.pageY - parentOffset.top;
      xValue = Math.round(chart.axisX[0].convertPixelToValue(relX));
      yValue = Math.round(chart.axisY[0].convertPixelToValue(relY));
      var dps = chart.data[0].dataPoints;
      for(var i = 0; i < dps.length; i++ ) {
        if((xValue >= dps[i].x - precisionLevel && xValue <= dps[i].x + precisionLevel) && (yValue >= dps[i].y - precisionLevel && yValue <= dps[i].y + precisionLevel) ) {
          record = true;
          selected = i;
          break;
        } else {
          selected = null;
          continue;
        }
      }
    },
    mousemove: function(e) {
      if(record ==  true) {
        parentOffset = $(this).parent().offset();
        relX = e.pageX - parentOffset.left;
        relY = e.pageY - parentOffset.top;
        xValue = Math.round(chart.axisX[0].convertPixelToValue(relX));
        yValue = Math.round(chart.axisY[0].convertPixelToValue(relY));
        clearTimeout(timerId);
        timerId = setTimeout(function(){
        if(selected != null) {
          chart.data[0].dataPoints[selected].x = xValue;
          chart.data[0].dataPoints[selected].y = yValue;
          chart.render();
        } 
        }, 0);
      }
    },
    mouseup: function(e) {
      if(selected != null) {
        chart.data[0].dataPoints[selected].x = xValue;
        chart.data[0].dataPoints[selected].y = yValue;
        chart.render();
        record = false;
      }
    }
  });
}</script>    



        <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
        <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
        <!--[if lt IE 9]>
          <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
            <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
        <![endif]-->
    </head>



<div class="site-index">
<div id="chartContainer" style="height: 300px; width: 100%;">
  </div>


                    <div class="row">
                        <div class="col-md-6">

                            <!-- Line chart -->
                            <div class="box box-primary">

                                <div class="box-header">
                                    <i class="fa fa-bar-chart-o"></i>
                                    <h3 class="box-title">Bar Chart</h3>
                                </div>

                          <div class="box-body">
<?= ChartJs::widget([
    'type' => 'bar',
    'options' => [
        'height' => 50,
        'width' => 50
    ],
    'data' => [
        'labels' => ["January", "February", "March", "April", "May","June","July","August","September","October","November","December"],
        'datasets' => [
            [
                'label' => "My First dataset",
                'backgroundColor' => "rgba(179,181,198,0.2)",
                'borderColor' => "rgba(179,181,198,1)",
                'pointBackgroundColor' => "rgba(179,181,198,1)",
                'pointBorderColor' => "#fff",
                'pointHoverBackgroundColor' => "#fff",
                'pointHoverBorderColor' => "rgba(179,181,198,1)",
                'data' => [65, 59, 90, 81, 56, 55, 40]
            ],
            [
                'label' => "My Second dataset",
                'backgroundColor' => "rgba(255,99,132,0.2)",
                'borderColor' => "rgba(255,99,132,1)",
                'pointBackgroundColor' => "rgba(255,99,132,1)",
                'pointBorderColor' => "#fff",
                'pointHoverBackgroundColor' => "#fff",
                'pointHoverBorderColor' => "rgba(255,99,132,1)",
                'data' => [28, 48, 40, 19, 96, 27, 100]
            ]
        ]
    ]
]);
?>                            
</div>
</div><!-- /.box -->


                            <!-- Area chart -->
                            <div class="box box-primary">
                                <div class="box-header">
                                    <i class="fa fa-bar-chart-o"></i>
                                    <h3 class="box-title">Full Width Area Chart</h3>
                                </div>
                                <div class="box-body">
<div id="chartContainer2" style="height: 300px; width: 100%;">
  </div>

                                </div><!-- /.box-body-->
                            </div><!-- /.box -->

                        </div><!-- /.col -->

                        <div class="col-md-6">
                            <!-- Bar chart -->
                            <div class="box box-primary">
                                <div class="box-header">
                                    <i class="fa fa-bar-chart-o"></i>
                                    <h3 class="box-title">Line Chart</h3>
                                </div>
                                <div class="box-body">
<?= ChartJs::widget([
    'type' => 'line',
    'options' => [
        'height' => 50,
        'width' => 50
    ],
    'data' => [
        'labels' => ["January", "February", "March", "April", "May"],
        'datasets' => [
            [
                'label' => "My First dataset",
                'backgroundColor' => "rgba(179,181,198,0.2)",
                'borderColor' => "rgba(179,181,198,1)",
                'pointBackgroundColor' => "rgba(179,181,198,1)",
                'pointBorderColor' => "#fff",
                'pointHoverBackgroundColor' => "#fff",
                'pointHoverBorderColor' => "rgba(179,181,198,1)",
                'data' => [65, 59, 90, 81, 56, 55, 40]
            ],
            [
                'label' => "My Second dataset",
                'backgroundColor' => "rgba(255,99,132,0.2)",
                'borderColor' => "rgba(255,99,132,1)",
                'pointBackgroundColor' => "rgba(255,99,132,1)",
                'pointBorderColor' => "#fff",
                'pointHoverBackgroundColor' => "#fff",
                'pointHoverBorderColor' => "rgba(255,99,132,1)",
                'data' => [28, 48, 40, 19, 96, 27, 100]
            ]
        ]
    ]
]);
?>                            

                                </div><!-- /.box-body-->
                            </div><!-- /.box -->

                            <!-- Donut chart -->
                            <div class="box box-primary">
                                <div class="box-header">
                                    <i class="fa fa-bar-chart-o"></i>
                                    <h3 class="box-title">Donut Chart</h3>
                                </div>
                                <div class="box-body">
<div id="chartContainer3" style="height: 300px; width: 100%;">
  </div>                                </div><!-- /.box-body-->
                            </div><!-- /.box -->
                        </div><!-- /.col -->
                    </div><!-- /.row -->



</div>

  <script src="../../backend/web/adminlte/js/jquery.min.js"></script>


