<?php

namespace backend\controllers;

use common\components\CsvExport;
use Exception;
use Yii;
use yii\web\Response;
use common\models\Order;
use common\models\Shade;
use common\models\Orderdetail;
use common\models\LedgerSearch;
use common\models\LedgerSalesmanSearch;
use common\models\CustomerProfile;
use common\models\BarcodeForm;
use common\models\SalesmanProfile;
use common\models\InventoryOut;
use FPDF;
use mPDF;
use common\models\Efpdf;

use common\models\Barcode;
use common\models\Ledger;
use common\models\LedgerCustomerSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\db\Query;
use yii\filters\AccessControl;

class LedgerController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                    'delete-all' => ['POST'],
                ],
            ],
            'access' => [
                'class' => AccessControl::class,
                'denyCallback' => function ($rule, $action) {
                    Yii::$app->getSession()->setFlash('error', 'You are not allowed to access that page');
                    return $this->redirect(Yii::$app->request->referrer);
                },
                'rules' => [
                    [
                        'allow' => false,
                        'roles' => ['user'],
                    ],
                    [
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
        ];
    }

    /**
     * Lists all Order models.
     * @return mixed
     */
    public function actionIndex()
    {
        // app->getUser()->identity->role;
        // var_dump(Yii::$app->authManager->getRoles());
        // var_dump(Yii::$app->user->identity->role);
        // return 'a';
        $searchModel = new LedgerSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }




    public function actionSalesman($id)
    {
        if (($SalesmanProfile = SalesmanProfile::findOne($id)) !== null) {
            $title = $SalesmanProfile->salesman_name.' - Ledgers';
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    
        $searchModel = new LedgerSalesmanSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $id);
        
        return $this->render('salesman', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'title' => $title,
            'id' => $id,
        ]);

    }

    public function actionCustomer($id)
    {
        if (($CustomerProfile = CustomerProfile::findOne($id)) !== null) {
            $title = $CustomerProfile->customer_name.' - Ledgers';
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    
        $searchModel = new LedgerCustomerSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $id);
        
        return $this->render('customer', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'title' => $title,
            'id' => $id,
        ]);

    }



    public function actionCreate()
    {
        $model = new Ledger();
        $url = 'index';

        if ($model->load(Yii::$app->request->post()) ){
            
            if ($model->person_type == 'salesman') {
                if (!$model->salesman_table_id) {
                    Yii::$app->getSession()->setFlash('error', 'Salesman is required');
                    return $this->redirect(Yii::$app->request->referrer);
                }
                $model->customer_table_id = NULL;
                $url = 'ledger/salesman/'.$model->salesman_table_id;
            } else {
                if (!$model->customer_table_id) {
                    Yii::$app->getSession()->setFlash('error', 'Customer is required');
                    return $this->redirect(Yii::$app->request->referrer);
                }
                $model->salesman_table_id = NULL;
                $url = 'ledger/customer/'.$model->customer_table_id;
            }
            
            if ($model->type == 'credit') {
                $model->credit = $model->amount;
            } else {
                $model->debit = $model->amount;
            }
            $model->save();
    
            Yii::$app->getSession()->setFlash('success', 'New '.strtoupper($model->type).' Entry added in the Ledger successfully!');
            return $this->redirect([$url]);

        } else {
            return $this->render('create', [
                'model' => $model
            ]);
        }
    }

    public function actionCustomerCreate($id)
    {
        $model = new Ledger();

        if ($model->load(Yii::$app->request->post()) ){
            
            $model->customer_table_id = $id;
            $model->salesman_table_id = NULL;
            if ($model->type == 'credit') {
                $model->credit = $model->amount;
            } else {
                $model->debit = $model->amount;
            }

            if ($model->save()) {
                Yii::$app->getSession()->setFlash('success', strtoupper($model->type).' Entry created in the Ledger successfully!');
            } else {
                Yii::$app->getSession()->setFlash('error', strtoupper($model->type).' Entry not created in the Ledger');
            }
            return $this->redirect(['ledger/customer/'.$id]);

        } else {
            return $this->render('customer-create', [
                'model' => $model
            ]);
        }

    }

    public function actionSalesmanCreate($id)
    {
        $model = new Ledger();

        if ($model->load(Yii::$app->request->post()) ){
            
            $model->salesman_table_id = $id;
            $model->customer_table_id = NULL;
            if ($model->type == 'credit') {
                $model->credit = $model->amount;
            } else {
                $model->debit = $model->amount;
            }

            if ($model->save()) {
                Yii::$app->getSession()->setFlash('success', strtoupper($model->type).' Entry created in the Ledger successfully!');
            } else {
                Yii::$app->getSession()->setFlash('error', strtoupper($model->type).' Entry not created in the Ledger');
            }
            return $this->redirect(['ledger/salesman/'.$id]);

        } else {
            return $this->render('salesman-create', [
                'model' => $model
            ]);
        }

    }





    

    public function actionCustomerExport($id)
    {
        $ledgers = Ledger::find()->where(['=','customer_table_id',$id])->orderBy(['ledger_date' => SORT_ASC])->asArray()->all();
        $CustomerProfile = CustomerProfile::findOne($id);
        $output = fopen("php://output",'w') or die("Can't open php://output");
        header("Content-Type:application/csv"); 
        header("Content-Disposition:attachment;filename=".$CustomerProfile->customer_name."-(".count($ledgers).")-Ledgers-Entries".".csv"); 
        fputcsv($output, array('Customer Name','Particular','Debit','Credit','Ledger Date','Created Date'));
        foreach($ledgers as $ledger) {
            fputcsv($output, [$CustomerProfile->customer_name, $ledger['particular'], $ledger['debit'], $ledger['credit'], $ledger['ledger_date'], $ledger['created_at']]);
        }
        fclose($output) or die("Can't close php://output");
    }

    public function actionSalesmanExport($id)
    {
        $ledgers = Ledger::find()->where(['=','salesman_table_id',$id])->orderBy(['ledger_date' => SORT_ASC])->asArray()->all();
        $salesmanProfile = SalesmanProfile::findOne($id);
        $output = fopen("php://output",'w') or die("Can't open php://output");
        header("Content-Type:application/csv"); 
        header("Content-Disposition:attachment;filename=".$salesmanProfile->salesman_name."-(".count($ledgers).")-Ledgers-Entries".".csv"); 
        fputcsv($output, array('salesman Name','Particular','Debit','Credit','Ledger Date','Created Date'));
        foreach($ledgers as $ledger) {
            fputcsv($output, [$salesmanProfile->salesman_name, $ledger['particular'], $ledger['debit'], $ledger['credit'], $ledger['ledger_date'], $ledger['created_at']]);
        }
        fclose($output) or die("Can't close php://output");
    }



    public function actionCustomerPdfExport($id)
    {
        $ledgers = Ledger::find()->where(['=','customer_table_id',$id])->orderBy(['ledger_date' => SORT_ASC])->asArray()->all();
        $CustomerProfile = CustomerProfile::findOne($id);

        $items = '';
        $balance = 0;
        foreach($ledgers as $ledger) {
            $balance += $ledger['debit']-$ledger['credit'];
            $items.='
            <tr>
                <td>'.$ledger['ledger_date'].'</td>
                <td>'.$ledger['particular'].'</td>
                <td>'. ($ledger['debit'] ? number_format($ledger['debit']) : '-' ).'</td>
                <td>'. ($ledger['credit'] ? number_format($ledger['credit']) : '-' ).'</td>
                <td>'.number_format($balance).'</td>
            </tr>';
        }


        $custom = '
            <!DOCTYPE html>
            <html lang="en">
            
                <head>
                    <meta charset="utf-8">
                    <meta http-equiv="X-UA-Compatible" content="IE=edge">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                    <title>'.$CustomerProfile->customer_name.' - Ledger Sheet</title>
                
                    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.1/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
                    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.1/js/bootstrap.min.js"></script>
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
                
                </head>
                <style>
                    @media print {
                        .new-page { page-break-before:auto;page-break-after:auto;page-break-inside:avoid }

                        .print-header {
                            display: table-header-group;
                            position: fixed;
                            top: 0pt;
                            left: 0pt;
                            right: 0pt;
                            text-align: left;
                        }
                    }
                </style>
                
                <body onload="window.print()">
                
                    <!-- Invoice template -->
                    <div class="container" style="padding: 0px; margin: 0px; max-width: initial;">
                        <div class="row">
                            <div class="col-12">
                                <div class="row">
                                    <div class="col-md-12 text-right">
                                        <p class="mb-1">'.date('l, F, Y h:i:s A').'</p>
                                    </div>
                                </div>
                                <div class="row pt-5">
                                    <div class="col-md-12 text-center">
                                        <p><h1><b>'.$CustomerProfile->customer_name.'</b></h1><h4>Customer Ledger Sheet</h4></p>
                                    </div>
                                </div>
                
                                <hr class="my-5">
                
                                <div class="row">
                                    <div class="col-md-12">
                                        <table class="table" style="font-size:x-large;">
                                            <thead>
                                                <tr>
                                                    <th colspan="5" class="border-0">
                                                        <div class="row pb-5">
                                                            <div class="col-md-5">
                                                                <p class="font-weight-bold mb-2">Customer Information</p>
                                                                <p class="mb-1"><span class="text-muted">Name : </span> '.$CustomerProfile->customer_name.'</p>
                                                                <p class="mb-1"><span class="text-muted">Address : </span> '.$CustomerProfile->address.'</p>
                                                                <p class="mb-1"><span class="text-muted">Contact : </span> '.$CustomerProfile->phone_number1.'</p>
                                                                <p class="mb-1"><span class="text-muted">Type : </span> '.$CustomerProfile->customer_type.'</p>
                                                            </div>
                
                                                            <div class="col-md-7 text-right">
                                                                <p class="font-weight-bold mb-2">Company Details</p>
                                                                <p class="mb-1"><span class="text-muted">Company : </span> Syed Enterprise (Color Box)</p>
                                                                <p class="mb-1"><span class="text-muted">Address : </span> North Karachi Industrial Area, Karachi</p>
                                                                <p class="mb-1"><span class="text-muted">Contact : </span> 0324-2949207, 0335-2573075</p>
                                                                <p class="mb-1"><span class="text-muted">Email : </span>  <EMAIL></p>
                                                            </div>
                                                        </div>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <th class="border-0 text-uppercase small font-weight-bold">Ledger Date</th>
                                                    <th class="border-0 text-uppercase small font-weight-bold">Particular</th>
                                                    <th class="border-0 text-uppercase small font-weight-bold">Debit</th>
                                                    <th class="border-0 text-uppercase small font-weight-bold">Credit</th>
                                                    <th class="border-0 text-uppercase small font-weight-bold">Balance</th>
                                                </tr>
                                            </thead>
                                            <tbody style="font-size:x-large;">
                                                '.$items.'
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                
                                <div class="new-page">
                                    <div class="d-flex flex-row-reverse p-1">
                                        <div class="py-3 px-5 text-right">
                                            <div class="mb-2"> Total Balance</div>
                                            <div class="h1 font-weight-light"> PKR '.number_format($balance).'</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /invoice template -->
                
                </body>
                
            
            </html>
        ';

        return $custom;
    }

    public function actionSalesmanPdfExport($id)
    {
        $ledgers = Ledger::find()->where(['=','Salesman_table_id',$id])->orderBy(['ledger_date' => SORT_ASC])->asArray()->all();
        $SalesmanProfile = SalesmanProfile::findOne($id);

        $items = '';
        $balance = 0;
        foreach($ledgers as $ledger) {
            $balance += $ledger['debit']-$ledger['credit'];
            $items.='
            <tr>
                <td>'.$ledger['ledger_date'].'</td>
                <td>'.$ledger['particular'].'</td>
                <td>'. ($ledger['debit'] ? number_format($ledger['debit']) : '-' ).'</td>
                <td>'. ($ledger['credit'] ? number_format($ledger['credit']) : '-' ).'</td>
                <td>'.number_format($balance).'</td>
            </tr>';
        }


        $custom = '
            <!DOCTYPE html>
            <html lang="en">
            
                <head>
                    <meta charset="utf-8">
                    <meta http-equiv="X-UA-Compatible" content="IE=edge">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                    <title>'.$SalesmanProfile->salesman_name.' - Ledger Sheet</title>
                
                    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.1/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
                    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.1/js/bootstrap.min.js"></script>
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
                
                </head>
                <style>
                    @media print {
                        .new-page { page-break-before:auto;page-break-after:auto;page-break-inside:avoid }

                        .print-header {
                            display: table-header-group;
                            position: fixed;
                            top: 0pt;
                            left: 0pt;
                            right: 0pt;
                            text-align: left;
                        }
                    }
                </style>
                
                <body onload="window.print()">
                
                    <!-- Invoice template -->
                    <div class="container" style="padding: 0px; margin: 0px; max-width: initial;">
                        <div class="row">
                            <div class="col-12">
                                <div class="row">
                                    <div class="col-md-12 text-right">
                                        <p class="mb-1">'.date('l, F, Y h:i:s A').'</p>
                                    </div>
                                </div>
                                <div class="row pt-5">
                                    <div class="col-md-12 text-center">
                                        <p><h1><b>'.$SalesmanProfile->salesman_name.'</b></h1><h4>Salesman Ledger Sheet</h4></p>
                                    </div>
                                </div>
                
                                <hr class="my-5">
                
                                
                                
                                <div class="row">
                                    <div class="col-md-12">
                                        <table class="table" style="font-size:x-large;">

                                            <thead>
                                                <tr>
                                                    <th colspan="5" class="border-0">
                                                        <div class="row pb-5">
                                                            <div class="col-md-5">
                                                                <p class="font-weight-bold mb-2">Salesman Information</p>
                                                                <p class="mb-1"><span class="text-muted">Name : </span> '.$SalesmanProfile->salesman_name.'</p>
                                                                <p class="mb-1"><span class="text-muted">Address : </span> '.$SalesmanProfile->address.'</p>
                                                                <p class="mb-1"><span class="text-muted">Contact : </span> '.$SalesmanProfile->phone_number.'</p>
                                                            </div>
                                        
                                                            <div class="col-md-7 text-right">
                                                                <p class="font-weight-bold mb-2">Company Details</p>
                                                                <p class="mb-1"><span class="text-muted">Company : </span> Syed Enterprise (Color Box)</p>
                                                                <p class="mb-1"><span class="text-muted">Address : </span> North Karachi Industrial Area, Karachi</p>
                                                                <p class="mb-1"><span class="text-muted">Contact : </span> 0324-2949207, 0335-2573075</p>
                                                                <p class="mb-1"><span class="text-muted">Email : </span>  <EMAIL></p>
                                                            </div>
                                                        </div>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <th class="border-0 text-uppercase small font-weight-bold">Ledger Date</th>
                                                    <th class="border-0 text-uppercase small font-weight-bold">Particular</th>
                                                    <th class="border-0 text-uppercase small font-weight-bold">Debit</th>
                                                    <th class="border-0 text-uppercase small font-weight-bold">Credit</th>
                                                    <th class="border-0 text-uppercase small font-weight-bold">Balance</th>
                                                </tr>
                                            </thead>
                                            <tbody style="font-size:x-large;">
                                                '.$items.'
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                
                                <div class="new-page">
                                    <div class="d-flex flex-row-reverse p-1">
                                        <div class="py-3 px-5 text-right">
                                            <div class="mb-2"> Total Balance</div>
                                            <div class="h1 font-weight-light"> PKR '.number_format($balance).'</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /invoice template -->
                
                </body>
                
            
            </html>
        ';

        return $custom;
    }





    public function actionUpdate($id)
    {
        
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && Yii::$app->user->identity->name=="Admin"){
            
            if ($model->type == 'credit') {
                $model->credit = $model->amount;
                $model->debit = 0;
            } else {
                $model->credit = 0;
                $model->debit = $model->amount;
            }
    
            if ($model->save()) {
                Yii::$app->getSession()->setFlash('success', strtoupper($model->type).' Entry updated in the Ledger successfully!');
            } else {
                Yii::$app->getSession()->setFlash('error', strtoupper($model->type).' Entry not updated in the Ledger');
            }
            

            if ($model->salesman_table_id) {
                return $this->redirect(['salesman', 'id' => $model->salesman_table_id]);
            } else {
                return $this->redirect(['customer', 'id' => $model->customer_table_id]);
            }
            
            

        } else {

            if ($model->credit != 0) {
                $model->type = 'credit';
                $model->amount = $model->credit;
            } else {
                $model->type = 'debit';
                $model->amount = $model->debit;
            }
            
            return $this->render('update', [
                'model' => $model
            ]);
        }

    }

    public function actionDelete($id)
    {
        return $this->redirect(['index']);

        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

        public function actionDeleteAll($id, $type)
    {
        // Only allow Admin users to delete
        if (Yii::$app->user->identity->name !== "Admin") {
            Yii::$app->getSession()->setFlash('error', 'You do not have permission to delete ledger entries.');
            return $this->redirect(['index']);
        }

        $entityName = '';
        $deletedCount = 0;

        try {
            if ($type == 1) {
                // Delete all entries for salesman
                $salesmanProfile = SalesmanProfile::findOne($id);
                if ($salesmanProfile === null) {
                    throw new NotFoundHttpException('The requested salesman does not exist.');
                }
                $entityName = $salesmanProfile->salesman_name;
                $deletedCount = Ledger::deleteAll(['salesman_table_id' => $id]);
            } else {
                // Delete all entries for customer
                $customerProfile = CustomerProfile::findOne($id);
                if ($customerProfile === null) {
                    throw new NotFoundHttpException('The requested customer does not exist.');
                }
                $entityName = $customerProfile->customer_name;
                $deletedCount = Ledger::deleteAll(['customer_table_id' => $id]);
            }

            if ($deletedCount > 0) {
                Yii::$app->getSession()->setFlash('success',
                    "Successfully deleted {$deletedCount} ledger entries for {$entityName}.");
            } else {
                Yii::$app->getSession()->setFlash('info',
                    "No ledger entries found for {$entityName}.");
            }

        } catch (Exception $e) {
            Yii::$app->getSession()->setFlash('error',
                'An error occurred while deleting ledger entries: ' . $e->getMessage());
        }

        return $this->redirect(['index']);
    }

    protected function findModel($id)
    {
        if (($model = Ledger::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }

    }
}
